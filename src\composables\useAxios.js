import axios from 'axios'
import { config } from '../utils/config.js'

// Get API base URL from centralized config
const API_BASE_URL = config.API_BASE_URL

// Create axios instance with base configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  }
})

// Request interceptor for debugging
apiClient.interceptors.request.use(
  (config) => {
    console.log('Axios Request:', config.method?.toUpperCase(), config.url, config.baseURL + config.url)
    return config
  },
  (error) => {
    console.error('Axios Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('Axios Response Error:', error.response?.status, error.response?.data || error.message)
    return Promise.reject(error)
  }
)

export function useAxios() {
  return {
    apiClient,
    // Convenience methods
    get: (url, config = {}) => apiClient.get(url, config),
    post: (url, data = {}, config = {}) => apiClient.post(url, data, config),
    put: (url, data = {}, config = {}) => apiClient.put(url, data, config),
    delete: (url, config = {}) => apiClient.delete(url, config),
    patch: (url, data = {}, config = {}) => apiClient.patch(url, data, config)
  }
}

// Export the configured axios instance for direct use
export { apiClient }
export default apiClient
