<template>
  <div class="min-h-screen bg-white p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-4xl font-bold text-gray-900 mb-8">Tailwind CSS Test Page</h1>
      
      <!-- Color Test -->
      <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">Colors</h2>
        <div class="grid grid-cols-6 gap-4">
          <div class="bg-red-500 h-16 rounded flex items-center justify-center text-white">Red</div>
          <div class="bg-blue-500 h-16 rounded flex items-center justify-center text-white">Blue</div>
          <div class="bg-green-500 h-16 rounded flex items-center justify-center text-white">Green</div>
          <div class="bg-yellow-500 h-16 rounded flex items-center justify-center text-white">Yellow</div>
          <div class="bg-purple-500 h-16 rounded flex items-center justify-center text-white">Purple</div>
          <div class="bg-pink-500 h-16 rounded flex items-center justify-center text-white">Pink</div>
        </div>
      </div>

      <!-- Layout Test -->
      <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">Layout</h2>
        <div class="flex space-x-4">
          <div class="flex-1 bg-gray-100 p-4 rounded">
            <h3 class="font-medium text-gray-900">Flex Item 1</h3>
            <p class="text-gray-600">This is a flex item with flex-1 class.</p>
          </div>
          <div class="flex-1 bg-gray-200 p-4 rounded">
            <h3 class="font-medium text-gray-900">Flex Item 2</h3>
            <p class="text-gray-600">This is another flex item with flex-1 class.</p>
          </div>
        </div>
      </div>

      <!-- Button Test -->
      <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">Buttons</h2>
        <div class="space-x-4">
          <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 rounded transition">
            Primary Button
          </button>
          <button class="bg-gray-600 hover:bg-gray-700 text-white font-medium px-4 py-2 rounded transition">
            Secondary Button
          </button>
          <button class="border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium px-4 py-2 rounded transition">
            Outline Button
          </button>
        </div>
      </div>

      <!-- Card Test -->
      <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">Cards</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-2">Card Title 1</h3>
            <p class="text-gray-600">This is a card with shadow and border styling.</p>
          </div>
          <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-2">Card Title 2</h3>
            <p class="text-gray-600">Another card to test responsive grid layout.</p>
          </div>
          <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-2">Card Title 3</h3>
            <p class="text-gray-600">Third card for testing purposes.</p>
          </div>
        </div>
      </div>

      <!-- Status -->
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-green-800">
              ✅ Tailwind CSS is working properly!
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Test page for Tailwind CSS functionality
</script>
