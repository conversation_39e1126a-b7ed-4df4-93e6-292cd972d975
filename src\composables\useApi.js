import { ref } from 'vue'
import { config } from '../utils/config.js'

// Get API base URL from centralized config
const API_BASE_URL = config.API_BASE_URL

export function useApi() {
  const loading = ref(false)
  const error = ref(null)

  // Helper function to build full URL
  const buildUrl = (endpoint) => {
    // If endpoint already starts with http, return as is
    if (endpoint.startsWith('http')) {
      return endpoint
    }

    // Remove leading slash from endpoint if present
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint

    // Ensure base URL ends with slash
    const baseUrl = API_BASE_URL.endsWith('/') ? API_BASE_URL : `${API_BASE_URL}/`

    return `${baseUrl}${cleanEndpoint}`
  }

  const apiCall = async (url, options = {}) => {
    loading.value = true
    error.value = null

    try {
      const fullUrl = buildUrl(url)
      console.log('API Call:', fullUrl) // Debug log

      const response = await fetch(fullUrl, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          ...options.headers
        },
        ...options
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`)
      }

      return data
    } catch (err) {
      error.value = err.message
      console.error('API Error:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const get = async (url, params = {}) => {
    try {
      const urlParams = new URLSearchParams(params)
      const fullUrl = urlParams.toString() ? `${url}?${urlParams}` : url
      const data = await apiCall(fullUrl, { method: 'GET' })

      // Return in the format expected by useAuth
      return {
        ok: true,
        data: data.data || data,
        hasAccess: data.hasAccess || false
      }
    } catch (err) {
      return {
        ok: false,
        error: err.message
      }
    }
  }

  const post = (url, data = {}) => {
    return apiCall(url, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  return {
    loading,
    error,
    get,
    post,
    apiCall
  }
}
