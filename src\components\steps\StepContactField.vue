<template>
  <div class="flex flex-col min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="flex-shrink-0 p-6 bg-white border-b border-gray-200">
      <h2 class="text-2xl font-bold text-gray-900 mb-3">Contact Field Mapping</h2>
      <p class="text-sm text-gray-600 leading-relaxed">
        Mapping contact data fields ensures that information seamlessly moves between RMS and HubSpot,
        maintaining accuracy and consistency for your HubSpot contacts.
        <a href="#" class="text-blue-600 hover:text-blue-800 underline">Learn more</a>
      </p>
    </div>

    <!-- Table Container with <PERSON>y Header -->
    <div class="flex-1 flex flex-col overflow-hidden bg-white">
      <!-- Sticky Table Header -->
      <div class="sticky top-0 z-10 bg-gray-50 border-b border-gray-200">
        <div class="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-gray-900">
          <div class="col-span-5">HubSpot Contact Field</div>
          <div class="col-span-5">RMS Field</div>
          <div class="col-span-2 text-center">Action</div>
        </div>
      </div>

      <!-- Scrollable Table Body -->
      <div class="flex-1 overflow-y-auto pb-4" style="max-height: calc(100vh - 220px);">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex items-center justify-center py-16">
          <div class="text-center">
            <svg class="animate-spin mx-auto h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Loading...</h3>
            <p class="mt-1 text-sm text-gray-500">Fetching HubSpot contact fields...</p>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!isLoading && (!contact || contact.length === 0)" class="flex items-center justify-center py-16">
          <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No fields to map</h3>
            <p class="mt-1 text-sm text-gray-500">No HubSpot contact fields found.</p>
          </div>
        </div>

        <!-- Table Rows -->
        <div v-if="!isLoading && contact && contact.length > 0"
             v-for="(item, index) in contact" :key="item.name"
             class="grid grid-cols-12 gap-4 px-6 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150"
             :class="{ 'mb-4': index === contact.length - 1 }">
          <!-- HubSpot Field -->
          <div class="col-span-5 flex items-center">
            <div class="min-w-0 flex-1">
              <div class="text-sm font-medium text-gray-900 truncate">{{ item.label }}</div>
              <div class="text-xs text-gray-500 truncate">{{ item.name }}</div>
            </div>
          </div>

          <!-- RMS Field Dropdown -->
          <div class="col-span-5 flex items-center">
            <select
              v-model="item.field"
              class="w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors duration-150"
              :class="{
                'border-green-400 bg-green-50 text-green-800': item.field && item.field !== '',
                'border-gray-300': !item.field || item.field === ''
              }"
            >
              <option value="">Select RMS Field...</option>
              <option v-for="f in getMatchingRmsFields(item)" :key="f.id" :value="f">{{ f.field_name }} ({{ f.field_type || f.type }})</option>
            </select>
          </div>

          <!-- Action Button -->
          <div class="col-span-2 flex items-center justify-center">
            <button
              class="inline-flex items-center p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              @click="openRemoveModal(item, index)"
              title="Remove field mapping"
            >
              <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor" class="flex-shrink-0">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6.6665 2.49935C6.6665 2.03911 7.0396 1.66602 7.49984 1.66602H12.4998C12.9601 1.66602 13.3332 2.03911 13.3332 2.49935C13.3332 2.95959 12.9601 3.33268 12.4998 3.33268H7.49984C7.0396 3.33268 6.6665 2.95959 6.6665 2.49935ZM4.16004 4.16602H2.49984C2.0396 4.16602 1.6665 4.53911 1.6665 4.99935C1.6665 5.45959 2.0396 5.83268 2.49984 5.83268H3.38688L3.92162 13.8539C3.96358 14.4834 3.9983 15.0044 4.06056 15.4284C4.12539 15.8699 4.22821 16.2734 4.44241 16.6494C4.77586 17.2347 5.27883 17.7052 5.88503 17.999C6.27444 18.1877 6.68383 18.2635 7.12866 18.2988C7.55591 18.3327 8.07803 18.3327 8.70895 18.3327H11.2907C11.9216 18.3327 12.4438 18.3327 12.871 18.2988C13.3158 18.2635 13.7252 18.1877 14.1146 17.999C14.7208 17.7052 15.2238 17.2347 15.5573 16.6494C15.7715 16.2734 15.8743 15.8699 15.9391 15.4284C16.0014 15.0043 16.0361 14.4833 16.0781 13.8538L16.6128 5.83268H17.4998C17.9601 5.83268 18.3332 5.45959 18.3332 4.99935C18.3332 4.53911 17.9601 4.16602 17.4998 4.16602H15.8396C15.8348 4.16597 15.8299 4.16597 15.8251 4.16602H4.17462C4.16977 4.16597 4.16491 4.16597 4.16004 4.16602ZM14.9424 5.83268H5.05724L5.5824 13.71C5.62712 14.3808 5.65804 14.8355 5.70955 15.1863C5.75958 15.5271 5.82071 15.7017 5.89057 15.8244C6.05729 16.117 6.30877 16.3523 6.61188 16.4992C6.73887 16.5607 6.91722 16.6101 7.26055 16.6373C7.61403 16.6654 8.06972 16.666 8.74205 16.666H11.2576C11.93 16.666 12.3856 16.6654 12.7391 16.6373C13.0825 16.6101 13.2608 16.5607 13.3878 16.4992C13.6909 16.3523 13.9424 16.117 14.1091 15.8244C14.179 15.7017 14.2401 15.5271 14.2901 15.1863C14.3416 14.8355 14.3726 14.3808 14.4173 13.71L14.9424 5.83268ZM8.33317 7.91602C8.79341 7.91602 9.1665 8.28911 9.1665 8.74935V12.916C9.1665 13.3763 8.79341 13.7493 8.33317 13.7493C7.87293 13.7493 7.49984 13.3763 7.49984 12.916V8.74935C7.49984 8.28911 7.87293 7.91602 8.33317 7.91602ZM11.6665 7.91602C12.1267 7.91602 12.4998 8.28911 12.4998 8.74935V12.916C12.4998 13.3763 12.1267 13.7493 11.6665 13.7493C11.2063 13.7493 10.8332 13.3763 10.8332 12.916V8.74935C10.8332 8.28911 11.2063 7.91602 11.6665 7.91602Z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex-shrink-0 bg-white border-t border-gray-200 sticky bottom-0 z-20">
      <div class="flex justify-between mt-6 px-6 py-4">
        <div class="flex space-x-4">
            <button
              class="border border-gray-300 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100"
              @click="$emit('back')"
            >
        Back

      </button>
        <button
            class="border border-gray-300 hover:border-blue-700 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100"
            @click="addModalVisible = true"
        >
        Add HubSpot field
        </button>
        </div>
        
      <button
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        @click="saveFieldMappings"
        :disabled="mappedFieldsCount === 0"
      >
        Next
        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
      </button>
    </div>

    </div>

    <AddFieldModal
        :show="addModalVisible"
        :rmsOptions="unusedRmsFields"
        :hubspotOptions="hubspotFieldTypeOptions"
        @close="addModalVisible = false"
        @add="handleAdd"
    />

    <ConfirmRemoveModal
        :show="modalVisible"
        :fieldName="selectedField?.label || ''"
        :type="'contact'"
        @close="modalVisible = false"
        @confirm="handleRemoveConfirm"
    />

    <!-- Toast Container -->
    <ToastContainer ref="toastContainer" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useAxios } from '../../composables/useAxios.js'

import AddFieldModal from '../AddFieldModal.vue'
import ConfirmRemoveModal from '../ConfirmRemoveModal.vue'
import ToastContainer from '../ToastContainer.vue'
import { useToast } from '../../composables/useToast.js'

const emit = defineEmits(['next', 'back']);

// Axios setup
const { get, post } = useAxios()

// Toast setup
const toastContainer = ref(null);
const { success, error, warning, info } = useToast();

const addModalVisible = ref(false);
const modalVisible = ref(false);
const hubspotFields = ref([]);
const hubspotFieldTypeOptions = ref([]);
const rmsFields = ref([]);
const contact = ref([]);
const selectedField = ref(null);
const selectedFieldIndex = ref(null);
const isLoading = ref(true);

const mappedFieldsCount = computed(() => {
  return Array.isArray(contact.value) ? contact.value.filter(item => item.field && item.field !== '').length : 0;
});

const unusedRmsFields = computed(() => {
  if (!Array.isArray(rmsFields.value) || !Array.isArray(contact.value)) {
    return [];
  }

  const selectedRmsFieldIds = contact.value
    .filter(item => item.field && item.field !== '')
    .map(item => item.field.id);

  return rmsFields.value.filter(rmsField => !selectedRmsFieldIds.includes(rmsField.id));
});

// Function to get RMS fields that match the HubSpot field type
const getMatchingRmsFields = (hubspotField) => {
  if (!hubspotField || !hubspotField.type || !Array.isArray(rmsFields.value)) {
    return [];
  }

  // Get already selected RMS fields (excluding current field)
  const selectedRmsFields = contact.value
    .filter(item => item.field && item.field !== '' && item.name !== hubspotField.name)
    .map(item => item.field.id);

  return rmsFields.value.filter(rmsField => {
    // Skip if this RMS field is already selected by another HubSpot field
    if (selectedRmsFields.includes(rmsField.id)) {
      return false;
    }
    const rmsFieldType = rmsField.field_type;
    const hubspotType = hubspotField.type;

    // Handle type mappings between RMS and HubSpot
    if (hubspotType === 'number') {
      // HubSpot 'number' matches RMS 'integer', 'double', 'float'
      return ['integer', 'double', 'float', 'number'].includes(rmsFieldType);
    }

    if (hubspotType === 'bool') {
      // HubSpot 'bool' matches RMS 'boolean'
      return ['boolean', 'bool'].includes(rmsFieldType);
    }

    if (hubspotType === 'date' || hubspotType === 'datetime') {
      // HubSpot 'date'/'datetime' matches RMS 'date', 'datetime'
      return ['date', 'datetime'].includes(rmsFieldType);
    }

    if (hubspotType === 'string') {
      // HubSpot 'string' matches RMS 'string', 'text'
      return ['string', 'text'].includes(rmsFieldType);
    }

    // Default: exact match for other types
    return rmsFieldType === hubspotType;
  });
};

onMounted(async () => {
  // Initialize toast container after DOM is ready
  await nextTick();
  if (toastContainer.value) {
    const { setToastContainer } = useToast();
    setToastContainer(toastContainer.value);
  }

  try {
    isLoading.value = true;
    const portalId = new URLSearchParams(window.location.search).get('portal_id')

    if (!portalId) {
      error('Missing Portal ID', 'Portal ID is required in the URL to proceed.');
      isLoading.value = false;
      return
    }

    const [hubRes, rmsRes, savedMappingsRes] = await Promise.all([
      get(`api/hubspot/contact-fields?portal_id=${portalId}`),
      get('api/contact/rms-fields'),
      get(`api/contact/field-mapping?portal_id=${portalId}`)
    ]);

    // Handle response
    hubspotFields.value = hubRes.data.data || [];
    rmsFields.value = rmsRes.data.data || [];
    const savedMappings = savedMappingsRes.data.data || [];

    // Extract unique combinations of fieldType and type
    if (Array.isArray(hubspotFields.value) && hubspotFields.value.length > 0) {
      const uniqueCombinations = new Map();

      hubspotFields.value.forEach(field => {
        if (field.fieldType && field.type) {
          const key = `${field.fieldType}-${field.type}`;
          if (!uniqueCombinations.has(key)) {
            uniqueCombinations.set(key, {
              fieldType: field.fieldType,
              type: field.type,
              label: `${field.fieldType} (${field.type})`
            });
          }
        }
      });

      hubspotFieldTypeOptions.value = Array.from(uniqueCombinations.values());
    }

    // hubspot fields for mapping
    if (Array.isArray(hubspotFields.value) && hubspotFields.value.length > 0) {
      // Filter out read-only fields (only show fields with readOnlyValue=false)
      const editableFields = hubspotFields.value.filter(field => {
        return field.readOnlyValue === false;
      });

      contact.value = editableFields.map(field => {
        // Find saved mapping for this HubSpot field
        const savedMapping = savedMappings.find(mapping => mapping.hubspot_field === field.name);
        let selectedRmsField = '';

        if (savedMapping) {
          // Find the corresponding RMS field object
          selectedRmsField = rmsFields.value.find(rmsField =>
            rmsField.field_name === savedMapping.rms_field
          ) || '';
        }

        return {
          name: field.name,
          label: field.label,
          type: field.type,
          fieldType: field.fieldType,
          field: selectedRmsField
        };
      });
      
    } else {
      contact.value = [];
    }
  } catch (error) {
    hubspotFields.value = [];
    rmsFields.value = [];
    contact.value = [];
    error('Failed to Load Data', 'Unable to load contact field data. Please refresh the page and try again.');
  } finally {
    isLoading.value = false;
  }
});

const openRemoveModal = (field, index) => {
  selectedField.value = field;
  selectedFieldIndex.value = index;
  modalVisible.value = true;
};

const handleRemoveConfirm = async () => {
  try {
    const portalId = new URLSearchParams(window.location.search).get('portal_id');

    if (!portalId) {
      error('Missing Portal ID', 'Portal ID is required to remove fields.');
      return;
    }

    // payload for remove API
    const payload = {
      portal_id: parseInt(portalId),
      name: selectedField.value.name,
    };

    const response = await post('api/hubspot/remove-contact-fields', payload);

    if (response.data.ok) {
      contact.value.splice(selectedFieldIndex.value, 1);
      success('Field Removed', `HubSpot contact field "${selectedField.value.label}" has been removed successfully.`);
    } else {
      error('Removal Failed', response.data.error || 'Failed to remove HubSpot contact field. Please try again.');
    }

  } catch (error) {
    error('Removal Error', 'An error occurred while removing the HubSpot contact field. Please try again.');
  }

  modalVisible.value = false;
  selectedField.value = null;
  selectedFieldIndex.value = null;
};

const handleAdd = async (data) => {
  try {
    const portalId = new URLSearchParams(window.location.search).get('portal_id')

    if (!portalId) {
      error('Missing Portal ID', 'Portal ID is required to create fields.');
      return
    }

    // Create field name by removing spaces and replacing with underscores
    const fieldName = data.hubspotField.toLowerCase().replace(/\s+/g, '_')
    const rmsField = data.rmsField.field_name;
    // Extract type and fieldType from selected option
    const selectedFieldType = data.hubspotFieldType || {};
    const fieldType = selectedFieldType.fieldType || "text";
    const type = selectedFieldType.type || "string";

    const payload = {
      "portal_id": parseInt(portalId),
      "rms_field": rmsField,
      "data": {
        "name": fieldName,
        "label": data.hubspotField,
        "groupName": "contactinformation",
        "type": type,
        "fieldType": fieldType,
        "formField": true
      }
    }

    const response = await post('api/hubspot/add-contact-fields', payload)

    if (response.data.ok) {
      contact.value.push({
        name: fieldName,
        label: data.hubspotField,
        type: type,
        fieldType: fieldType,
        field: data.rmsField || ''
      })
      success('Field Created', `HubSpot contact field "${data.hubspotField}" has been created successfully!`);
    } else {
      error('Creation Failed', response.data.error || 'Failed to create HubSpot contact field. Please try again.');
    }

  } catch (error) {
    error('Creation Error', 'An error occurred while creating the HubSpot contact field. Please try again.');
  }

  addModalVisible.value = false
}

// Save field mappings to database
const saveFieldMappings = async () => {
  try {
    const portalId = new URLSearchParams(window.location.search).get('portal_id');

    if (!portalId) {
      error('Missing Portal ID', 'Portal ID is required to save field mappings.');
      return;
    }

    // Get only mapped fields
    const mappedFields = contact.value.filter(item => item.field && item.field !== '');

    if (mappedFields.length === 0) {
      warning('No Fields Mapped', 'Please map at least one field before proceeding to the next step.');
      return;
    }

    // Create payload with mapped fields
    const payload = {
      portal_id: parseInt(portalId),
      mappings: mappedFields.map(item => ({
        hubspot_field: item.name,
        rms_field: item.field.field_name
      }))
    };

    const response = await post('api/contact/fields-mapping', payload);

    if (response.data.ok) {
      success('Mappings Saved', `Successfully saved ${mappedFields.length} contact field mapping(s). Proceeding to next step.`);
      setTimeout(() => {
        emit('next')
      }, 1500)
    } else {
      error('Save Failed', response.data.error || 'Failed to save field mappings. Please try again.');
    }

  } catch (error) {
    error('Save Error', 'An error occurred while saving field mappings. Please try again.');
  }
};

</script>
