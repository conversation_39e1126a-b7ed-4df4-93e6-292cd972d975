<template>
  <div>
    <h1 class="text-4xl font-bold text-blue-600 mb-4">Simple Tailwind Test</h1>
    
    <!-- Direct Tailwind classes -->
    <div class="bg-red-500 text-white p-4 rounded mb-4">
      Red background with white text
    </div>
    
    <div class="bg-blue-500 text-white p-4 rounded mb-4">
      Blue background with white text
    </div>
    
    <div class="bg-green-500 text-white p-4 rounded mb-4">
      Green background with white text
    </div>
    
    <!-- Test custom class -->
    <div class="test-tailwind mb-4">
      Custom Tailwind class test
    </div>
    
    <!-- Flexbox test -->
    <div class="flex space-x-4 mb-4">
      <div class="flex-1 bg-gray-200 p-2">Flex 1</div>
      <div class="flex-1 bg-gray-300 p-2">Flex 2</div>
      <div class="flex-1 bg-gray-400 p-2">Flex 3</div>
    </div>
    
    <!-- Grid test -->
    <div class="grid grid-cols-3 gap-4 mb-4">
      <div class="bg-purple-200 p-2">Grid 1</div>
      <div class="bg-purple-300 p-2">Grid 2</div>
      <div class="bg-purple-400 p-2">Grid 3</div>
    </div>
    
    <p class="text-sm text-gray-600">
      If you see colors and proper spacing above, Tailwind is working correctly.
    </p>
  </div>
</template>

<script setup>
// Simple test component
</script>

<style>
@import '../assets/tailwind-test.css';
</style>
