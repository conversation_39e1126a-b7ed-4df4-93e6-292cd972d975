<template>
  <div class="min-h-screen bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-4xl font-bold text-red-600 mb-8">Tailwind Debug Page</h1>
      
      <!-- Basic Color Test -->
      <div class="mb-8">
        <h2 class="text-2xl font-semibold mb-4">Color Test</h2>
        <div class="flex space-x-4">
          <div class="w-20 h-20 bg-red-500"></div>
          <div class="w-20 h-20 bg-blue-500"></div>
          <div class="w-20 h-20 bg-green-500"></div>
          <div class="w-20 h-20 bg-yellow-500"></div>
        </div>
      </div>

      <!-- Layout Test -->
      <div class="mb-8">
        <h2 class="text-2xl font-semibold mb-4">Layout Test</h2>
        <div class="grid grid-cols-2 gap-4">
          <div class="bg-white p-4 rounded shadow">Grid Item 1</div>
          <div class="bg-white p-4 rounded shadow">Grid Item 2</div>
        </div>
      </div>

      <!-- Button Test -->
      <div class="mb-8">
        <h2 class="text-2xl font-semibold mb-4">Button Test</h2>
        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
          Test Button
        </button>
      </div>

      <!-- Spacing Test -->
      <div class="mb-8">
        <h2 class="text-2xl font-semibold mb-4">Spacing Test</h2>
        <div class="space-y-4">
          <div class="p-4 bg-gray-200">Padding 4</div>
          <div class="p-8 bg-gray-300">Padding 8</div>
          <div class="m-4 p-4 bg-gray-400">Margin 4, Padding 4</div>
        </div>
      </div>

      <!-- Typography Test -->
      <div class="mb-8">
        <h2 class="text-2xl font-semibold mb-4">Typography Test</h2>
        <p class="text-sm text-gray-600">Small text</p>
        <p class="text-base text-gray-700">Base text</p>
        <p class="text-lg text-gray-800">Large text</p>
        <p class="text-xl font-bold text-gray-900">Extra large bold text</p>
      </div>

      <!-- Status -->
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        If you can see styled colors, spacing, and typography above, Tailwind is working!
      </div>
    </div>
  </div>
</template>

<script setup>
// Simple debug page to test Tailwind CSS
</script>
