import { ref } from 'vue'

// Global toast container reference
const toastContainer = ref(null)

export function useToast() {
  const setToastContainer = (container) => {
    toastContainer.value = container
    console.log('Toast container set:', container)
  }

  const addToast = (options) => {
    console.log('Adding toast:', options, 'Container:', toastContainer.value)

    if (!toastContainer.value) {
      console.warn('Toast container not initialized, trying to show toast anyway')
      // Fallback to browser alert if toast system fails
      const message = `${options.title}: ${options.message}`
      alert(message)
      return
    }

    return toastContainer.value.addToast(options)
  }

  const success = (title, message = '', options = {}) => {
    return addToast({ ...options, type: 'success', title, message })
  }

  const error = (title, message = '', options = {}) => {
    return addToast({ ...options, type: 'error', title, message })
  }

  const warning = (title, message = '', options = {}) => {
    return addToast({ ...options, type: 'warning', title, message })
  }

  const info = (title, message = '', options = {}) => {
    return addToast({ ...options, type: 'info', title, message })
  }

  const clearAll = () => {
    if (toastContainer.value) {
      toastContainer.value.clearAll()
    }
  }

  return {
    setToastContainer,
    success,
    error,
    warning,
    info,
    clearAll
  }
}
