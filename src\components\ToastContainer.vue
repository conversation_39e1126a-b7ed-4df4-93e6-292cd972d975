<template>
  <div class="fixed top-4 right-4 z-50 space-y-3 pointer-events-none">
    <Toast
      v-for="toast in toasts"
      :key="toast.id"
      :type="toast.type"
      :title="toast.title"
      :message="toast.message"
      :duration="toast.duration"
      :auto-close="toast.autoClose"
      @close="removeToast(toast.id)"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Toast from './Toast.vue'

const toasts = ref([])
let nextId = 1

const addToast = (options) => {
  const toast = {
    id: nextId++,
    type: options.type || 'info',
    title: options.title,
    message: options.message || '',
    duration: options.duration || 5000,
    autoClose: options.autoClose !== false
  }

  toasts.value.push(toast)

  // Limit to 5 toasts maximum
  if (toasts.value.length > 5) {
    toasts.value.shift()
  }

  return toast.id
}

const removeToast = (id) => {
  const index = toasts.value.findIndex(toast => toast.id === id)
  if (index > -1) {
    toasts.value.splice(index, 1)
  }
}

const clearAll = () => {
  toasts.value = []
}

// Expose methods for parent components
defineExpose({
  addToast,
  removeToast,
  clearAll,
  success: (title, message, options = {}) => addToast({ ...options, type: 'success', title, message }),
  error: (title, message, options = {}) => addToast({ ...options, type: 'error', title, message }),
  warning: (title, message, options = {}) => addToast({ ...options, type: 'warning', title, message }),
  info: (title, message, options = {}) => addToast({ ...options, type: 'info', title, message })
})
</script>
