<template>
    <DefaultLayout>
        <div class="flex items-center justify-center min-h-screen bg-white px-4">
    <div class="border border-dotted border-blue-300 rounded-lg p-10 max-w-md text-center shadow-sm">
      <!-- Image -->
      <img
        src="/images/success.png"
        alt="Success"
        class="mx-auto mb-6"
      />

      <!-- Heading -->
      <h2 class="text-lg font-semibold text-gray-900 mb-2">You’re all set!</h2>

      <!-- Description -->
      <p class="text-sm text-gray-600 mb-6">
        Your HubSpot account is now connected to your Firmable account. <br />
        Head to either your contact or company records to start finding new prospects or customer data.
      </p>

      <!-- Buttons -->
      <div class="flex flex-col sm:flex-row justify-center gap-3">
        <a
          href="#"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-100"
        >
          Take me to HubSpot contacts
        </a>
        <a
          href="#"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-100"
        >
          Take me to HubSpot companies
        </a>
      </div>
    </div>
  </div>
    </DefaultLayout>

</template>

<script setup>
import DefaultLayout from '../layouts/DefaultLayout.vue';
</script>
