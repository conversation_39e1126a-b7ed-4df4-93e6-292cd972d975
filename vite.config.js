import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import tailwindcss from '@tailwindcss/vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    tailwindcss(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  // base: './crmbuild/',
  build: {
    outDir: "D:/xampp8.0/htdocs/crmseries/public/crmbuild",
    emptyOutDir: false, // Optional: prevent deleting contents of the target folder
  },
  base: '/rmscrmseries/public/crmbuild/',
})
