<template>
  <div v-if="show" class="fixed inset-0 bg-black/30 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md relative shadow-lg">
      <!-- Close Button -->
      <button @click="close" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-xl font-bold">
        &times;
      </button>

      <!-- Title -->
      <h2 class="text-lg font-semibold text-gray-900 mb-2">
        Remove from mappings
      </h2>

      <!-- Message -->
      <p class="text-sm text-gray-700 mb-6">
        You are about to remove “<span class="font-medium">{{ fieldName }}</span>” field from {{ type }} field mappings.
      </p>

      <!-- Action Buttons -->
      <div class="flex justify-end space-x-4">
        <button @click="close" class="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100">
          Cancel
        </button>
        <button @click="confirm" class="bg-red-100 text-red-600 hover:bg-red-200 font-medium px-4 py-2 rounded-lg">
          Remove
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  show: Boolean,
  type: {
    type: String,
    required: true
  },
  fieldName: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['close', 'confirm'])

const close = () => emit('close')
const confirm = () => emit('confirm')
</script>
