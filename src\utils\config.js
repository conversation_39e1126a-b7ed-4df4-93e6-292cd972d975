// Configuration utility for environment variables
export const config = {
  // API Configuration
  API_BASE_URL: import.meta.env.VITE_API_URL || import.meta.env.VUE_APP_API_URL || 'http://localhost:8000/',
  
  // Development mode check
  isDevelopment: import.meta.env.DEV,
  
  // Production mode check
  isProduction: import.meta.env.PROD,
  
  // Environment mode
  mode: import.meta.env.MODE,
  
  // Debug function to log all environment variables (development only)
  logEnvVars() {
    if (this.isDevelopment) {
      console.group('🔧 Environment Configuration')
      console.log('API Base URL:', this.API_BASE_URL)
      console.log('Mode:', this.mode)
      console.log('Development:', this.isDevelopment)
      console.log('Production:', this.isProduction)
      console.log('All VITE_ vars:', Object.keys(import.meta.env).filter(key => key.startsWith('VITE_')))
      console.groupEnd()
    }
  }
}

// Auto-log in development
if (config.isDevelopment) {
  config.logEnvVars()
}
